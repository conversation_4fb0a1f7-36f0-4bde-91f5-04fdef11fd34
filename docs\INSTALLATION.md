# Installation Guide

## System Requirements

### Minimum Requirements
- **Operating System**: Windows 10, macOS 10.14, or Linux (Ubuntu 18.04+)
- **Python**: 3.9 or higher
- **RAM**: 4 GB minimum, 8 GB recommended
- **Storage**: 500 MB free space
- **Display**: 1024x768 minimum resolution

### Recommended Requirements
- **Operating System**: Windows 11, macOS 12+, or Linux (Ubuntu 20.04+)
- **Python**: 3.11 or higher
- **RAM**: 8 GB or more
- **Storage**: 2 GB free space
- **Display**: 1920x1080 or higher

## Installation Steps

### 1. Install Python
Download and install Python from [python.org](https://www.python.org/downloads/)

**Windows:**
- Download the Windows installer
- Check "Add Python to PATH" during installation
- Verify installation: `python --version`

**macOS:**
- Use Homebrew: `brew install python3`
- Or download from python.org
- Verify installation: `python3 --version`

**Linux:**
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

### 2. Clone or Download the Project
```bash
git clone <repository-url>
cd peer-review-system-2
```

### 3. Set Up Virtual Environment
```bash
# Create virtual environment
python -m venv venv

# Activate virtual environment
# Windows:
venv\Scripts\activate
# macOS/Linux:
source venv/bin/activate
```

### 4. Install Dependencies
```bash
# Using the run script (recommended)
python run.py install

# Or manually
pip install -r requirements.txt
```

### 5. Set Up Development Environment
```bash
python run.py setup
```

This will:
- Install all dependencies
- Create necessary directories
- Set up initial configuration

### 6. Run the Application
```bash
python run.py run
```

## Default Login Credentials

After first installation, use these credentials to log in:

- **Username**: `admin`
- **Password**: `Admin@123`

**⚠️ IMPORTANT**: Change the default password immediately after first login!

## Troubleshooting

### Common Issues

#### 1. Python Not Found
**Error**: `'python' is not recognized as an internal or external command`

**Solution**: 
- Ensure Python is installed and added to PATH
- Try using `python3` instead of `python`

#### 2. Permission Denied
**Error**: Permission denied when installing packages

**Solution**:
- Use virtual environment (recommended)
- Or install with user flag: `pip install --user -r requirements.txt`

#### 3. Qt Platform Plugin Error
**Error**: `qt.qpa.plugin: Could not load the Qt platform plugin`

**Solution**:
- Install system Qt libraries
- Windows: Usually resolved by installing Visual C++ Redistributable
- Linux: `sudo apt install qt6-base-dev`

#### 4. Database Initialization Error
**Error**: Database fails to initialize

**Solution**:
- Check file permissions in the project directory
- Ensure the `data` directory is writable
- Try resetting the database: `python run.py reset-db`

### Getting Help

1. Check the logs in the `logs/` directory
2. Run tests to verify installation: `python run.py test`
3. Contact support: <EMAIL>

## Development Setup

For developers who want to contribute:

### 1. Install Development Dependencies
```bash
pip install -r requirements.txt
pip install pytest pytest-qt pytest-cov black flake8 mypy
```

### 2. Run Tests
```bash
python run.py test
```

### 3. Code Quality Checks
```bash
# Format code
black src/

# Check style
flake8 src/

# Type checking
mypy src/
```

### 4. Database Management
```bash
# Reset database (WARNING: Deletes all data)
python run.py reset-db
```

## Configuration

### Application Settings
Settings are stored in the system's standard location:
- **Windows**: `%APPDATA%/Ajinkyacreatiion PVT. LTD./Military Peer Review System/`
- **macOS**: `~/Library/Preferences/com.ajinkyacreatiion.Military Peer Review System.plist`
- **Linux**: `~/.config/Ajinkyacreatiion PVT. LTD./Military Peer Review System.conf`

### Database Location
By default, the database is stored in:
- `data/peer_review.db` (relative to application directory)

### Logs Location
Application logs are stored in:
- `logs/app.log` (relative to application directory)

## Uninstallation

To completely remove the application:

1. Delete the application directory
2. Remove settings (optional):
   - Windows: Delete the folder in `%APPDATA%`
   - macOS: Delete the plist file
   - Linux: Delete the config file
3. Remove any backups you created

## Security Considerations

1. **Change Default Passwords**: Always change default passwords after installation
2. **File Permissions**: Ensure proper file permissions on the database and logs
3. **Network Security**: The application runs locally and doesn't require network access
4. **Data Backup**: Regularly backup your database using the built-in backup feature
5. **Updates**: Keep the application updated for security patches
