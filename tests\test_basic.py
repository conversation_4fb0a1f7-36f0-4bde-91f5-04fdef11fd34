"""
Basic tests for the peer review system
"""

import pytest
import sys
import os

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))


def test_imports():
    """Test that basic imports work."""
    try:
        from config.settings import AppSettings
        from core.auth import AuthenticationManager
        from core.encryption import DataEncryption
        from models.user import User
        from utils.validators import validate_email
        assert True
    except ImportError as e:
        pytest.fail(f"Import failed: {e}")


def test_settings():
    """Test settings initialization."""
    from config.settings import AppSettings
    settings = AppSettings()
    # Test that settings can be retrieved
    db_path = settings.get("database/path")
    assert db_path is not None
    assert isinstance(db_path, str)


def test_validators():
    """Test validation functions."""
    from utils.validators import validate_email, validate_username

    # Test email validation
    assert validate_email("<EMAIL>") == True
    assert validate_email("invalid-email") == False

    # Test username validation
    assert validate_username("valid_user123") == True
    assert validate_username("ab") == False  # Too short
    assert validate_username("user@invalid") == False  # Invalid characters


def test_password_hashing():
    """Test password hashing functionality."""
    from core.encryption import PasswordHasher

    hasher = PasswordHasher()
    password = "TestPassword123!"

    # Hash password
    hashed = hasher.hash_password(password)
    assert hashed is not None
    assert hashed != password

    # Verify password
    assert hasher.verify_password(password, hashed) == True
    assert hasher.verify_password("wrong_password", hashed) == False


if __name__ == "__main__":
    pytest.main([__file__])
