"""
Batch and Student Models

This module defines models for student batches and individual students.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey
from sqlalchemy.orm import relationship

from config.database import Base


class Batch(Base):
    """Batch model for grouping students."""
    
    __tablename__ = "batches"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Batch information
    name = Column(String(100), nullable=False)
    description = Column(Text, nullable=True)
    year = Column(Integer, nullable=False)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Metadata
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    modified_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Additional information
    notes = Column(Text, nullable=True)
    
    # Relationships
    created_by_user = relationship("User", back_populates="created_batches", foreign_keys=[created_by])
    students = relationship("Student", back_populates="batch", cascade="all, delete-orphan")
    assessments = relationship("Assessment", back_populates="target_batch")
    
    def __repr__(self):
        return f"<Batch(id={self.id}, name='{self.name}', year={self.year})>"
    
    def to_dict(self):
        """Convert batch to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "description": self.description,
            "year": self.year,
            "is_active": self.is_active,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
            "notes": self.notes,
            "student_count": len(self.students) if self.students else 0,
        }
    
    def get_active_students(self):
        """Get list of active students in this batch."""
        return [student for student in self.students if student.is_active]


class Student(Base):
    """Student model for individual students within batches."""
    
    __tablename__ = "students"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Student information
    student_id = Column(String(50), nullable=False, index=True)  # Unique student identifier
    full_name = Column(String(100), nullable=False)
    year_of_birth = Column(Integer, nullable=True)
    
    # Authentication (for student login if needed)
    password_hash = Column(String(255), nullable=True)
    
    # Contact information
    email = Column(String(100), nullable=True)
    phone = Column(String(20), nullable=True)
    
    # Batch relationship
    batch_id = Column(Integer, ForeignKey("batches.id"), nullable=False, index=True)
    
    # Status
    is_active = Column(Boolean, default=True, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    modified_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Additional information
    notes = Column(Text, nullable=True)
    
    # Relationships
    batch = relationship("Batch", back_populates="students")
    assessment_responses = relationship("AssessmentResponse", back_populates="student")
    peer_evaluations_given = relationship("PeerEvaluation", back_populates="evaluator", foreign_keys="PeerEvaluation.evaluator_id")
    peer_evaluations_received = relationship("PeerEvaluation", back_populates="evaluated", foreign_keys="PeerEvaluation.evaluated_id")
    
    def __repr__(self):
        return f"<Student(id={self.id}, student_id='{self.student_id}', name='{self.full_name}')>"
    
    def to_dict(self):
        """Convert student to dictionary."""
        return {
            "id": self.id,
            "student_id": self.student_id,
            "full_name": self.full_name,
            "year_of_birth": self.year_of_birth,
            "email": self.email,
            "phone": self.phone,
            "batch_id": self.batch_id,
            "batch_name": self.batch.name if self.batch else None,
            "is_active": self.is_active,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
            "notes": self.notes,
        }
    
    def get_age(self):
        """Calculate student's age based on year of birth."""
        if self.year_of_birth:
            current_year = datetime.now().year
            return current_year - self.year_of_birth
        return None
