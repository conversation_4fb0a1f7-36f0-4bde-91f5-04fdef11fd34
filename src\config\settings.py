"""
Application Settings and Configuration Management

This module handles all application settings, including database configuration,
security settings, UI preferences, and institution information.
"""

import os
import json
import logging
from pathlib import Path
from typing import Dict, Any, Optional


class AppSettings:
    """Application settings manager using JSON file for persistence."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.settings_file = Path("data/settings.json")
        self.settings_file.parent.mkdir(parents=True, exist_ok=True)

        self._settings = {}
        self._load_settings()
        self._ensure_default_settings()

    def _ensure_default_settings(self):
        """Ensure default settings are set if not already configured."""
        defaults = {
            # Database settings
            "database/path": "data/peer_review.db",
            "database/backup_enabled": True,
            "database/backup_interval_hours": 24,

            # Security settings
            "security/session_timeout_minutes": 30,
            "security/password_min_length": 8,
            "security/require_password_complexity": True,
            "security/max_login_attempts": 3,
            "security/lockout_duration_minutes": 15,

            # Institution settings
            "institution/name": "Military Training Institute",
            "institution/logo_path": "",
            "institution/contact_email": "",
            "institution/contact_phone": "",
            "institution/address": "",

            # UI settings
            "ui/theme": "default",
            "ui/font_size": 10,
            "ui/window_width": 1200,
            "ui/window_height": 800,
            "ui/remember_window_state": True,

            # Assessment settings
            "assessment/default_scale_min": 1,
            "assessment/default_scale_max": 7,
            "assessment/auto_save_interval_minutes": 5,
            "assessment/export_format": "pdf",

            # Analytics settings
            "analytics/outlier_detection_method": "iqr",
            "analytics/outlier_threshold": 1.5,
            "analytics/confidence_level": 0.95,

            # Report settings
            "reports/include_charts": True,
            "reports/include_comments": True,
            "reports/watermark_enabled": False,
            "reports/page_size": "A4",

            # System settings
            "system/auto_backup": True,
            "system/log_level": "INFO",
            "system/check_updates": True,
            "system/data_retention_days": 365,
        }

        for key, value in defaults.items():
            if key not in self._settings:
                self._settings[key] = value

        self._save_settings()

    def _load_settings(self):
        """Load settings from JSON file."""
        try:
            if self.settings_file.exists():
                with open(self.settings_file, 'r') as f:
                    self._settings = json.load(f)
        except Exception as e:
            self.logger.warning(f"Failed to load settings: {e}")
            self._settings = {}

    def _save_settings(self):
        """Save settings to JSON file."""
        try:
            with open(self.settings_file, 'w') as f:
                json.dump(self._settings, f, indent=2)
        except Exception as e:
            self.logger.error(f"Failed to save settings: {e}")

    def get(self, key: str, default: Any = None) -> Any:
        """Get a setting value."""
        return self._settings.get(key, default)

    def set(self, key: str, value: Any) -> None:
        """Set a setting value."""
        self._settings[key] = value
        self._save_settings()
        self.logger.debug(f"Setting updated: {key} = {value}")

    def get_database_path(self) -> Path:
        """Get the database file path."""
        db_path_str = self.get("database/path", "data/peer_review.db")
        if db_path_str is None:
            db_path_str = "data/peer_review.db"
        db_path = Path(db_path_str)
        db_path.parent.mkdir(parents=True, exist_ok=True)
        return db_path

    def get_institution_info(self) -> Dict[str, str]:
        """Get institution information."""
        return {
            "name": self.get("institution/name"),
            "logo_path": self.get("institution/logo_path"),
            "contact_email": self.get("institution/contact_email"),
            "contact_phone": self.get("institution/contact_phone"),
            "address": self.get("institution/address"),
        }

    def set_institution_info(self, info: Dict[str, str]) -> None:
        """Set institution information."""
        for key, value in info.items():
            self.set(f"institution/{key}", value)

    def get_security_settings(self) -> Dict[str, Any]:
        """Get security-related settings."""
        return {
            "session_timeout_minutes": self.get("security/session_timeout_minutes"),
            "password_min_length": self.get("security/password_min_length"),
            "require_password_complexity": self.get("security/require_password_complexity"),
            "max_login_attempts": self.get("security/max_login_attempts"),
            "lockout_duration_minutes": self.get("security/lockout_duration_minutes"),
        }

    def get_ui_settings(self) -> Dict[str, Any]:
        """Get UI-related settings."""
        return {
            "theme": self.get("ui/theme"),
            "font_size": self.get("ui/font_size"),
            "window_width": self.get("ui/window_width"),
            "window_height": self.get("ui/window_height"),
            "remember_window_state": self.get("ui/remember_window_state"),
        }

    def get_assessment_settings(self) -> Dict[str, Any]:
        """Get assessment-related settings."""
        return {
            "default_scale_min": self.get("assessment/default_scale_min"),
            "default_scale_max": self.get("assessment/default_scale_max"),
            "auto_save_interval_minutes": self.get("assessment/auto_save_interval_minutes"),
            "export_format": self.get("assessment/export_format"),
        }

    def get_analytics_settings(self) -> Dict[str, Any]:
        """Get analytics-related settings."""
        return {
            "outlier_detection_method": self.get("analytics/outlier_detection_method"),
            "outlier_threshold": self.get("analytics/outlier_threshold"),
            "confidence_level": self.get("analytics/confidence_level"),
        }

    def export_settings(self, file_path: Path) -> None:
        """Export settings to a JSON file."""
        with open(file_path, 'w') as f:
            json.dump(self._settings, f, indent=2)

        self.logger.info(f"Settings exported to {file_path}")

    def import_settings(self, file_path: Path) -> None:
        """Import settings from a JSON file."""
        with open(file_path, 'r') as f:
            imported_settings = json.load(f)

        self._settings.update(imported_settings)
        self._save_settings()
        self.logger.info(f"Settings imported from {file_path}")

    def reset_to_defaults(self) -> None:
        """Reset all settings to default values."""
        self._settings.clear()
        self._ensure_default_settings()
        self.logger.info("Settings reset to defaults")


# Global settings instance
app_settings = AppSettings()
