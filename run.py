#!/usr/bin/env python3
"""
Run script for Military Peer Review Assessment System

This script provides a convenient way to run the application
and perform common development tasks.
"""

import sys
import os
import argparse
import subprocess
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))


def run_application():
    """Run the main application."""
    try:
        from main import main
        return main()
    except KeyboardInterrupt:
        print("\nApplication interrupted by user")
        return 0
    except Exception as e:
        print(f"Error running application: {e}")
        return 1


def run_tests():
    """Run the test suite."""
    try:
        import pytest
        return pytest.main(["-v", "tests/"])
    except ImportError:
        print("pytest not installed. Install with: pip install pytest")
        return 1


def install_dependencies():
    """Install project dependencies."""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("Dependencies installed successfully")
        return 0
    except subprocess.CalledProcessError as e:
        print(f"Failed to install dependencies: {e}")
        return 1


def setup_development():
    """Set up development environment."""
    print("Setting up development environment...")
    
    # Install dependencies
    if install_dependencies() != 0:
        return 1
    
    # Create necessary directories
    directories = [
        "data",
        "logs",
        "backups",
        "exports",
        "resources/icons",
        "resources/styles",
        "resources/templates"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"Created directory: {directory}")
    
    print("Development environment setup complete!")
    return 0


def reset_database():
    """Reset the database to initial state."""
    print("WARNING: This will delete all data in the database!")
    response = input("Are you sure you want to continue? (yes/no): ")
    
    if response.lower() != 'yes':
        print("Database reset cancelled")
        return 0
    
    try:
        from config.database import db_manager
        from database.migrations import db_migrations
        
        # Initialize database manager
        db_manager.initialize()
        
        # Reset database
        db_migrations.reset_database()
        
        print("Database reset completed successfully")
        return 0
    except Exception as e:
        print(f"Database reset failed: {e}")
        return 1


def main():
    """Main entry point for the run script."""
    parser = argparse.ArgumentParser(description="Military Peer Review Assessment System")
    parser.add_argument(
        "command",
        choices=["run", "test", "install", "setup", "reset-db"],
        help="Command to execute"
    )
    
    args = parser.parse_args()
    
    if args.command == "run":
        return run_application()
    elif args.command == "test":
        return run_tests()
    elif args.command == "install":
        return install_dependencies()
    elif args.command == "setup":
        return setup_development()
    elif args.command == "reset-db":
        return reset_database()
    else:
        parser.print_help()
        return 1


if __name__ == "__main__":
    sys.exit(main())
