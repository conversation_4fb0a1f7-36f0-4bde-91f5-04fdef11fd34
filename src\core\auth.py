"""
Authentication and Authorization System

This module handles user authentication, session management, and role-based
access control for the peer review system.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from enum import Enum

from core.exceptions import AuthenticationError, AuthorizationError
from core.encryption import password_hasher
from config.settings import app_settings


class UserRole(Enum):
    """User roles with different permission levels."""
    SUPER_ADMIN = "super_admin"
    ADMIN = "admin"
    TEACHER = "teacher"
    STUDENT = "student"


class Permission(Enum):
    """System permissions."""
    # User management
    CREATE_USER = "create_user"
    EDIT_USER = "edit_user"
    DELETE_USER = "delete_user"
    VIEW_USERS = "view_users"
    
    # Batch management
    CREATE_BATCH = "create_batch"
    EDIT_BATCH = "edit_batch"
    DELETE_BATCH = "delete_batch"
    VIEW_BATCHES = "view_batches"
    
    # Assessment management
    CREATE_ASSESSMENT = "create_assessment"
    EDIT_ASSESSMENT = "edit_assessment"
    DELETE_ASSESSMENT = "delete_assessment"
    VIEW_ASSESSMENTS = "view_assessments"
    EXECUTE_ASSESSMENT = "execute_assessment"
    
    # Report access
    VIEW_REPORTS = "view_reports"
    GENERATE_REPORTS = "generate_reports"
    EXPORT_REPORTS = "export_reports"
    
    # System administration
    SYSTEM_SETTINGS = "system_settings"
    BACKUP_RESTORE = "backup_restore"
    VIEW_LOGS = "view_logs"


class RolePermissions:
    """Defines permissions for each role."""
    
    PERMISSIONS = {
        UserRole.SUPER_ADMIN: [
            # All permissions
            Permission.CREATE_USER, Permission.EDIT_USER, Permission.DELETE_USER, Permission.VIEW_USERS,
            Permission.CREATE_BATCH, Permission.EDIT_BATCH, Permission.DELETE_BATCH, Permission.VIEW_BATCHES,
            Permission.CREATE_ASSESSMENT, Permission.EDIT_ASSESSMENT, Permission.DELETE_ASSESSMENT,
            Permission.VIEW_ASSESSMENTS, Permission.EXECUTE_ASSESSMENT,
            Permission.VIEW_REPORTS, Permission.GENERATE_REPORTS, Permission.EXPORT_REPORTS,
            Permission.SYSTEM_SETTINGS, Permission.BACKUP_RESTORE, Permission.VIEW_LOGS,
        ],
        
        UserRole.ADMIN: [
            # User management (limited)
            Permission.CREATE_USER, Permission.EDIT_USER, Permission.VIEW_USERS,
            # Full batch management
            Permission.CREATE_BATCH, Permission.EDIT_BATCH, Permission.DELETE_BATCH, Permission.VIEW_BATCHES,
            # Full assessment management
            Permission.CREATE_ASSESSMENT, Permission.EDIT_ASSESSMENT, Permission.DELETE_ASSESSMENT,
            Permission.VIEW_ASSESSMENTS, Permission.EXECUTE_ASSESSMENT,
            # Full report access
            Permission.VIEW_REPORTS, Permission.GENERATE_REPORTS, Permission.EXPORT_REPORTS,
        ],
        
        UserRole.TEACHER: [
            # Limited batch management
            Permission.VIEW_BATCHES,
            # Assessment management
            Permission.CREATE_ASSESSMENT, Permission.EDIT_ASSESSMENT, Permission.VIEW_ASSESSMENTS,
            Permission.EXECUTE_ASSESSMENT,
            # Report access
            Permission.VIEW_REPORTS, Permission.GENERATE_REPORTS, Permission.EXPORT_REPORTS,
        ],
        
        UserRole.STUDENT: [
            # Very limited access
            Permission.VIEW_ASSESSMENTS,
        ],
    }
    
    @classmethod
    def has_permission(cls, role: UserRole, permission: Permission) -> bool:
        """Check if a role has a specific permission."""
        return permission in cls.PERMISSIONS.get(role, [])


class AuthenticationManager:
    """Manages user authentication and sessions."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self._current_user = None
        self._session_start_time = None
        self._login_attempts = {}
    
    def authenticate(self, username: str, password: str) -> Dict[str, Any]:
        """Authenticate a user with username and password."""
        try:
            # Check for account lockout
            if self._is_account_locked(username):
                raise AuthenticationError("Account is temporarily locked due to too many failed attempts")
            
            # Import here to avoid circular imports
            from services.user_service import UserService
            user_service = UserService()
            
            # Get user from database
            user = user_service.get_user_by_username(username)
            if not user:
                self._record_failed_attempt(username)
                raise AuthenticationError("Invalid username or password")
            
            # Verify password
            if not password_hasher.verify_password(password, user.password_hash):
                self._record_failed_attempt(username)
                raise AuthenticationError("Invalid username or password")
            
            # Check if user is active
            if not user.is_active:
                raise AuthenticationError("Account is disabled")
            
            # Clear failed attempts on successful login
            self._clear_failed_attempts(username)
            
            # Set current user and session
            self._current_user = user
            self._session_start_time = datetime.now()
            
            # Update last login time
            user_service.update_last_login(user.id)
            
            self.logger.info(f"User authenticated successfully: {username}")
            
            return {
                "user_id": user.id,
                "username": user.username,
                "full_name": user.full_name,
                "role": user.role,
                "email": user.email,
                "last_login": user.last_login,
            }
            
        except AuthenticationError:
            raise
        except Exception as e:
            self.logger.error(f"Authentication error: {e}")
            raise AuthenticationError(f"Authentication failed: {e}")
    
    def logout(self) -> None:
        """Log out the current user."""
        if self._current_user:
            self.logger.info(f"User logged out: {self._current_user.username}")
        
        self._current_user = None
        self._session_start_time = None
    
    def is_authenticated(self) -> bool:
        """Check if a user is currently authenticated."""
        if not self._current_user or not self._session_start_time:
            return False
        
        # Check session timeout
        timeout_minutes = app_settings.get("security/session_timeout_minutes")
        if datetime.now() - self._session_start_time > timedelta(minutes=timeout_minutes):
            self.logout()
            return False
        
        return True
    
    def get_current_user(self) -> Optional[Any]:
        """Get the currently authenticated user."""
        if self.is_authenticated():
            return self._current_user
        return None
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if the current user has a specific permission."""
        if not self.is_authenticated():
            return False
        
        user_role = UserRole(self._current_user.role)
        return RolePermissions.has_permission(user_role, permission)
    
    def require_permission(self, permission: Permission) -> None:
        """Require a specific permission, raise exception if not authorized."""
        if not self.is_authenticated():
            raise AuthenticationError("Authentication required")
        
        if not self.has_permission(permission):
            raise AuthorizationError(f"Permission denied: {permission.value}")
    
    def _is_account_locked(self, username: str) -> bool:
        """Check if an account is locked due to failed attempts."""
        if username not in self._login_attempts:
            return False
        
        attempts_data = self._login_attempts[username]
        max_attempts = app_settings.get("security/max_login_attempts")
        lockout_duration = app_settings.get("security/lockout_duration_minutes")
        
        if attempts_data["count"] >= max_attempts:
            time_since_last = datetime.now() - attempts_data["last_attempt"]
            if time_since_last < timedelta(minutes=lockout_duration):
                return True
            else:
                # Lockout period expired, clear attempts
                self._clear_failed_attempts(username)
        
        return False
    
    def _record_failed_attempt(self, username: str) -> None:
        """Record a failed login attempt."""
        if username not in self._login_attempts:
            self._login_attempts[username] = {"count": 0, "last_attempt": None}
        
        self._login_attempts[username]["count"] += 1
        self._login_attempts[username]["last_attempt"] = datetime.now()
        
        self.logger.warning(f"Failed login attempt for user: {username}")
    
    def _clear_failed_attempts(self, username: str) -> None:
        """Clear failed login attempts for a user."""
        if username in self._login_attempts:
            del self._login_attempts[username]
    
    def extend_session(self) -> None:
        """Extend the current session timeout."""
        if self.is_authenticated():
            self._session_start_time = datetime.now()


# Global authentication manager instance
auth_manager = AuthenticationManager()
