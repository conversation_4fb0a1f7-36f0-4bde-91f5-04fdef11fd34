"""
Database Connection Management

This module provides database connection utilities and session management.
"""

import logging
from contextlib import contextmanager
from typing import Generator
from sqlalchemy.orm import Session

from config.database import db_manager
from core.exceptions import DatabaseError


class DatabaseConnection:
    """Database connection manager with session handling."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get a database session with automatic cleanup."""
        session = None
        try:
            session = db_manager.get_session()
            yield session
            session.commit()
        except Exception as e:
            if session:
                session.rollback()
            self.logger.error(f"Database session error: {e}")
            raise DatabaseError(f"Database operation failed: {e}")
        finally:
            if session:
                session.close()
    
    def execute_transaction(self, func, *args, **kwargs):
        """Execute a function within a database transaction."""
        with self.get_session() as session:
            return func(session, *args, **kwargs)


# Global database connection instance
db_connection = DatabaseConnection()
