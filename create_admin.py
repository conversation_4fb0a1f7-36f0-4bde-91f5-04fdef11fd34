#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the default admin user for the peer review system.
"""

import sys
import os
from pathlib import Path
from datetime import datetime

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

def create_admin_user():
    """Create the default admin user."""
    try:
        from config.database import db_manager
        from models.user import User
        from core.encryption import password_hasher
        
        # Initialize database
        db_manager.initialize()
        
        # Create session
        session = db_manager.get_session()
        
        try:
            # Check if admin already exists
            existing_admin = session.query(User).filter(
                User.username == "admin"
            ).first()
            
            if existing_admin:
                print("Admin user already exists!")
                return
            
            # Create admin user
            admin_password = "Admin@123"
            password_hash = password_hasher.hash_password(admin_password)
            
            admin_user = User(
                username="admin",
                password_hash=password_hash,
                full_name="System Administrator",
                email="<EMAIL>",
                role="super_admin",
                is_active=True,
                is_verified=True,
                created_at=datetime.utcnow(),
                notes="Default system administrator account"
            )
            
            session.add(admin_user)
            session.commit()
            
            print("✅ Admin user created successfully!")
            print("Username: admin")
            print("Password: Admin@123")
            print("⚠️  IMPORTANT: Change the password immediately after first login!")
            
        except Exception as e:
            session.rollback()
            print(f"❌ Error creating admin user: {e}")
        finally:
            session.close()
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    create_admin_user()
