"""
User Model

This module defines the User model for authentication and user management.
"""

from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text
from sqlalchemy.orm import relationship

from config.database import Base


class User(Base):
    """User model for authentication and role management."""
    
    __tablename__ = "users"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Authentication fields
    username = Column(String(50), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    
    # Personal information
    full_name = Column(String(100), nullable=False)
    email = Column(String(100), unique=True, nullable=True)
    phone = Column(String(20), nullable=True)
    
    # Role and permissions
    role = Column(String(20), nullable=False, default="teacher")  # super_admin, admin, teacher, student
    
    # Status fields
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    modified_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    last_login = Column(DateTime, nullable=True)
    
    # Additional information
    notes = Column(Text, nullable=True)
    
    # Relationships
    created_batches = relationship("Batch", back_populates="created_by_user", foreign_keys="Batch.created_by")
    created_assessments = relationship("Assessment", back_populates="created_by_user")
    
    def __repr__(self):
        return f"<User(id={self.id}, username='{self.username}', role='{self.role}')>"
    
    def to_dict(self):
        """Convert user to dictionary (excluding sensitive data)."""
        return {
            "id": self.id,
            "username": self.username,
            "full_name": self.full_name,
            "email": self.email,
            "phone": self.phone,
            "role": self.role,
            "is_active": self.is_active,
            "is_verified": self.is_verified,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
            "last_login": self.last_login.isoformat() if self.last_login else None,
            "notes": self.notes,
        }
    
    def has_role(self, role: str) -> bool:
        """Check if user has a specific role."""
        return self.role == role
    
    def is_admin(self) -> bool:
        """Check if user is an admin or super admin."""
        return self.role in ["admin", "super_admin"]
    
    def is_super_admin(self) -> bool:
        """Check if user is a super admin."""
        return self.role == "super_admin"
