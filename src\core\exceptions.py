"""
Custom Exception Classes

This module defines custom exceptions used throughout the application
for better error handling and user feedback.
"""


class PeerReviewSystemError(Exception):
    """Base exception for all application-specific errors."""
    pass


class DatabaseError(PeerReviewSystemError):
    """Raised when database operations fail."""
    pass


class AuthenticationError(PeerReviewSystemError):
    """Raised when authentication fails."""
    pass


class AuthorizationError(PeerReviewSystemError):
    """Raised when user lacks required permissions."""
    pass


class ValidationError(PeerReviewSystemError):
    """Raised when data validation fails."""
    pass


class ConfigurationError(PeerReviewSystemError):
    """Raised when configuration is invalid or missing."""
    pass


class ImportError(PeerReviewSystemError):
    """Raised when data import operations fail."""
    pass


class ExportError(PeerReviewSystemError):
    """Raised when data export operations fail."""
    pass


class AssessmentError(PeerReviewSystemError):
    """Raised when assessment operations fail."""
    pass


class ReportGenerationError(PeerReviewSystemError):
    """Raised when report generation fails."""
    pass


class EncryptionError(PeerReviewSystemError):
    """Raised when encryption/decryption operations fail."""
    pass
