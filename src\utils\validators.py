"""
Data Validation Utilities

This module provides validation functions for various data types
used throughout the application.
"""

import re
import logging
from typing import List, Optional, Any
from datetime import datetime


def validate_email(email: str) -> bool:
    """Validate email address format."""
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None


def validate_username(username: str) -> bool:
    """Validate username format."""
    if not username:
        return False
    
    # Username should be 3-50 characters, alphanumeric and underscores only
    pattern = r'^[a-zA-Z0-9_]{3,50}$'
    return re.match(pattern, username) is not None


def validate_phone(phone: str) -> bool:
    """Validate phone number format."""
    if not phone:
        return True  # Phone is optional
    
    # Remove all non-digit characters
    digits_only = re.sub(r'\D', '', phone)
    
    # Should have 10-15 digits
    return 10 <= len(digits_only) <= 15


def validate_student_id(student_id: str) -> bool:
    """Validate student ID format."""
    if not student_id:
        return False
    
    # Student ID should be 3-20 characters, alphanumeric
    pattern = r'^[a-zA-Z0-9]{3,20}$'
    return re.match(pattern, student_id) is not None


def validate_year_of_birth(year: int) -> bool:
    """Validate year of birth."""
    if not year:
        return True  # Year of birth is optional
    
    current_year = datetime.now().year
    return 1900 <= year <= current_year


def validate_assessment_title(title: str) -> bool:
    """Validate assessment title."""
    if not title:
        return False
    
    # Title should be 3-200 characters
    return 3 <= len(title.strip()) <= 200


def validate_scale_range(min_value: int, max_value: int) -> bool:
    """Validate assessment scale range."""
    if min_value is None or max_value is None:
        return False
    
    # Min should be less than max, and range should be reasonable
    return min_value < max_value and 1 <= min_value <= 10 and 2 <= max_value <= 10


def validate_csv_headers(headers: List[str], required_headers: List[str]) -> tuple[bool, List[str]]:
    """Validate CSV file headers."""
    missing_headers = []
    
    # Convert to lowercase for case-insensitive comparison
    headers_lower = [h.lower().strip() for h in headers]
    required_lower = [h.lower() for h in required_headers]
    
    for required in required_lower:
        if required not in headers_lower:
            missing_headers.append(required)
    
    return len(missing_headers) == 0, missing_headers


def validate_batch_name(name: str) -> bool:
    """Validate batch name."""
    if not name:
        return False
    
    # Batch name should be 2-100 characters
    return 2 <= len(name.strip()) <= 100


def validate_question_text(text: str) -> bool:
    """Validate question text."""
    if not text:
        return False
    
    # Question text should be 5-1000 characters
    return 5 <= len(text.strip()) <= 1000


def validate_score_range(score: Any, min_val: int = 1, max_val: int = 7) -> bool:
    """Validate score within range."""
    try:
        score_int = int(score)
        return min_val <= score_int <= max_val
    except (ValueError, TypeError):
        return False


def validate_file_extension(filename: str, allowed_extensions: List[str]) -> bool:
    """Validate file extension."""
    if not filename:
        return False
    
    extension = filename.lower().split('.')[-1]
    return extension in [ext.lower() for ext in allowed_extensions]


def sanitize_filename(filename: str) -> str:
    """Sanitize filename for safe file system usage."""
    if not filename:
        return "untitled"
    
    # Remove or replace invalid characters
    invalid_chars = r'[<>:"/\\|?*]'
    sanitized = re.sub(invalid_chars, '_', filename)
    
    # Remove leading/trailing spaces and dots
    sanitized = sanitized.strip(' .')
    
    # Limit length
    if len(sanitized) > 255:
        sanitized = sanitized[:255]
    
    return sanitized or "untitled"


def validate_date_range(start_date: Optional[datetime], end_date: Optional[datetime]) -> bool:
    """Validate date range."""
    if start_date is None or end_date is None:
        return True  # Optional dates
    
    return start_date <= end_date


def validate_password_complexity(password: str) -> tuple[bool, List[str]]:
    """Validate password complexity requirements."""
    issues = []
    
    if len(password) < 8:
        issues.append("Password must be at least 8 characters long")
    
    if not re.search(r'[A-Z]', password):
        issues.append("Password must contain at least one uppercase letter")
    
    if not re.search(r'[a-z]', password):
        issues.append("Password must contain at least one lowercase letter")
    
    if not re.search(r'\d', password):
        issues.append("Password must contain at least one digit")
    
    if not re.search(r'[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]', password):
        issues.append("Password must contain at least one special character")
    
    return len(issues) == 0, issues


def validate_json_structure(data: Any, required_keys: List[str]) -> tuple[bool, List[str]]:
    """Validate JSON data structure."""
    if not isinstance(data, dict):
        return False, ["Data must be a JSON object"]
    
    missing_keys = []
    for key in required_keys:
        if key not in data:
            missing_keys.append(key)
    
    return len(missing_keys) == 0, missing_keys


def validate_numeric_range(value: Any, min_val: Optional[float] = None, max_val: Optional[float] = None) -> bool:
    """Validate numeric value within range."""
    try:
        num_val = float(value)
        
        if min_val is not None and num_val < min_val:
            return False
        
        if max_val is not None and num_val > max_val:
            return False
        
        return True
    except (ValueError, TypeError):
        return False


class ValidationResult:
    """Class to hold validation results."""
    
    def __init__(self, is_valid: bool = True, errors: Optional[List[str]] = None):
        self.is_valid = is_valid
        self.errors = errors or []
    
    def add_error(self, error: str):
        """Add an error to the validation result."""
        self.is_valid = False
        self.errors.append(error)
    
    def __bool__(self):
        """Return True if validation passed."""
        return self.is_valid


def validate_user_data(data: dict) -> ValidationResult:
    """Comprehensive user data validation."""
    result = ValidationResult()
    
    # Required fields
    required_fields = ['username', 'full_name', 'password']
    for field in required_fields:
        if field not in data or not data[field]:
            result.add_error(f"Field '{field}' is required")
    
    # Username validation
    if 'username' in data and data['username']:
        if not validate_username(data['username']):
            result.add_error("Username must be 3-50 characters, alphanumeric and underscores only")
    
    # Email validation
    if 'email' in data and data['email']:
        if not validate_email(data['email']):
            result.add_error("Invalid email format")
    
    # Phone validation
    if 'phone' in data and data['phone']:
        if not validate_phone(data['phone']):
            result.add_error("Invalid phone number format")
    
    # Password validation
    if 'password' in data and data['password']:
        is_valid, issues = validate_password_complexity(data['password'])
        if not is_valid:
            result.errors.extend(issues)
            result.is_valid = False
    
    return result


def validate_student_data(data: dict) -> ValidationResult:
    """Comprehensive student data validation."""
    result = ValidationResult()
    
    # Required fields
    required_fields = ['student_id', 'full_name']
    for field in required_fields:
        if field not in data or not data[field]:
            result.add_error(f"Field '{field}' is required")
    
    # Student ID validation
    if 'student_id' in data and data['student_id']:
        if not validate_student_id(data['student_id']):
            result.add_error("Student ID must be 3-20 characters, alphanumeric only")
    
    # Year of birth validation
    if 'year_of_birth' in data and data['year_of_birth']:
        try:
            year = int(data['year_of_birth'])
            if not validate_year_of_birth(year):
                result.add_error("Invalid year of birth")
        except (ValueError, TypeError):
            result.add_error("Year of birth must be a valid number")
    
    # Email validation
    if 'email' in data and data['email']:
        if not validate_email(data['email']):
            result.add_error("Invalid email format")
    
    return result
