"""
Database Migrations and Initial Data Setup

This module handles database schema migrations and initial data population.
"""

import logging
from datetime import datetime
from sqlalchemy.orm import Session

from models.user import User
from core.encryption import password_hasher
from database.connection import db_connection
from config.settings import app_settings


class DatabaseMigrations:
    """Handles database migrations and initial data setup."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)

    def run_initial_setup(self):
        """Run initial database setup including creating default admin user."""
        try:
            self._create_default_admin()
            self._create_sample_data()
            self.logger.info("Initial database setup completed successfully")
        except Exception as e:
            self.logger.error(f"Initial database setup failed: {e}")
            raise

    def _create_default_admin(self):
        """Create default super admin user if none exists."""
        try:
            from config.database import db_manager

            # Use direct session from db_manager
            session = db_manager.get_session()

            try:
                # Check if any super admin exists
                existing_admin = session.query(User).filter(
                    User.role == "super_admin",
                    User.is_active == True
                ).first()

                if existing_admin:
                    self.logger.info("Super admin user already exists")
                    return

                # Create default super admin
                admin_password = "Admin@123"  # Default password - should be changed immediately
                password_hash = password_hasher.hash_password(admin_password)

                admin_user = User(
                    username="admin",
                    password_hash=password_hash,
                    full_name="System Administrator",
                    email="<EMAIL>",
                    role="super_admin",
                    is_active=True,
                    is_verified=True,
                    created_at=datetime.utcnow(),
                    notes="Default system administrator account"
                )

                session.add(admin_user)
                session.commit()

                self.logger.info("Default super admin user created successfully")
                self.logger.warning("Default admin password is 'Admin@123' - CHANGE IMMEDIATELY!")

            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()

        except Exception as e:
            self.logger.error(f"Failed to create default admin user: {e}")
            raise

    def _create_sample_data(self):
        """Create sample data for demonstration purposes."""
        try:
            from config.database import db_manager

            # Use direct session from db_manager
            session = db_manager.get_session()

            try:
                # Check if sample users already exist
                existing_users = session.query(User).filter(
                    User.username.in_(["teacher1", "teacher2"])
                ).count()

                if existing_users > 0:
                    self.logger.info("Sample users already exist")
                    return

                # Create sample teacher users
                sample_users = [
                    {
                        "username": "teacher1",
                        "password": "Teacher@123",
                        "full_name": "Dr. John Smith",
                        "email": "<EMAIL>",
                        "role": "teacher",
                        "notes": "Sample teacher account"
                    },
                    {
                        "username": "teacher2",
                        "password": "Teacher@123",
                        "full_name": "Prof. Sarah Johnson",
                        "email": "<EMAIL>",
                        "role": "teacher",
                        "notes": "Sample teacher account"
                    }
                ]

                for user_data in sample_users:
                    password_hash = password_hasher.hash_password(user_data["password"])

                    user = User(
                        username=user_data["username"],
                        password_hash=password_hash,
                        full_name=user_data["full_name"],
                        email=user_data["email"],
                        role=user_data["role"],
                        is_active=True,
                        is_verified=True,
                        created_at=datetime.utcnow(),
                        notes=user_data["notes"]
                    )

                    session.add(user)

                session.commit()
                self.logger.info("Sample teacher users created successfully")

            except Exception as e:
                session.rollback()
                raise e
            finally:
                session.close()

        except Exception as e:
            self.logger.error(f"Failed to create sample data: {e}")
            # Don't raise exception for sample data creation failure

    def upgrade_database_schema(self):
        """Upgrade database schema to latest version."""
        try:
            # This method can be used for future schema upgrades
            self.logger.info("Database schema is up to date")
        except Exception as e:
            self.logger.error(f"Database schema upgrade failed: {e}")
            raise

    def reset_database(self):
        """Reset database to initial state (WARNING: This will delete all data)."""
        try:
            from config.database import Base, db_manager

            # Drop all tables
            Base.metadata.drop_all(bind=db_manager.engine)

            # Recreate all tables
            Base.metadata.create_all(bind=db_manager.engine)

            # Run initial setup
            self.run_initial_setup()

            self.logger.info("Database reset completed successfully")

        except Exception as e:
            self.logger.error(f"Database reset failed: {e}")
            raise


# Global migrations instance
db_migrations = DatabaseMigrations()
