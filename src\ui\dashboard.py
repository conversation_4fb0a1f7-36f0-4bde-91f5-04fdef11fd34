"""
Main Dashboard

This module provides the main dashboard interface showing system overview
and quick access to key features.
"""

import logging
from datetime import datetime, timedelta
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QLabel, QPushButton,
    QFrame, QScrollArea, QGroupBox, QProgressBar, QTableWidget, QTableWidgetItem,
    QHeaderView, QSpacerItem, QSizePolicy
)
from PySide6.QtCore import Qt, QTimer
from PySide6.QtGui import QFont, QPixmap, QIcon

from core.auth import auth_manager, Permission
from config.settings import app_settings


class StatCard(QFrame):
    """A card widget for displaying statistics."""
    
    def __init__(self, title: str, value: str, subtitle: str = "", color: str = "#3498db"):
        super().__init__()
        self.setFixedSize(200, 120)
        self.setStyleSheet(f"""
            QFrame {{
                background-color: white;
                border: 1px solid #e0e0e0;
                border-radius: 8px;
                border-left: 4px solid {color};
            }}
            QFrame:hover {{
                box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            }}
        """)
        
        layout = QVBoxLayout(self)
        layout.setContentsMargins(15, 15, 15, 15)
        layout.setSpacing(5)
        
        # Title
        title_label = QLabel(title)
        title_label.setStyleSheet("color: #7f8c8d; font-size: 12px; font-weight: bold;")
        layout.addWidget(title_label)
        
        # Value
        value_label = QLabel(value)
        value_font = QFont()
        value_font.setPointSize(24)
        value_font.setBold(True)
        value_label.setFont(value_font)
        value_label.setStyleSheet(f"color: {color};")
        layout.addWidget(value_label)
        
        # Subtitle
        if subtitle:
            subtitle_label = QLabel(subtitle)
            subtitle_label.setStyleSheet("color: #95a5a6; font-size: 11px;")
            layout.addWidget(subtitle_label)
        
        layout.addStretch()


class QuickActionButton(QPushButton):
    """A styled button for quick actions."""
    
    def __init__(self, text: str, icon_name: str = None):
        super().__init__(text)
        self.setFixedSize(150, 100)
        self.setStyleSheet("""
            QPushButton {
                background-color: #ecf0f1;
                border: 1px solid #bdc3c7;
                border-radius: 8px;
                font-size: 12px;
                font-weight: bold;
                color: #2c3e50;
                text-align: center;
            }
            QPushButton:hover {
                background-color: #d5dbdb;
                border-color: #95a5a6;
            }
            QPushButton:pressed {
                background-color: #bdc3c7;
            }
        """)


class Dashboard(QWidget):
    """Main dashboard widget."""
    
    def __init__(self):
        super().__init__()
        self.logger = logging.getLogger(__name__)
        
        # Data refresh timer
        self.refresh_timer = QTimer()
        self.refresh_timer.timeout.connect(self.refresh_data)
        self.refresh_timer.start(300000)  # Refresh every 5 minutes
        
        self._setup_ui()
        self.refresh_data()
    
    def _setup_ui(self):
        """Set up the dashboard UI."""
        main_layout = QVBoxLayout(self)
        main_layout.setContentsMargins(20, 20, 20, 20)
        main_layout.setSpacing(20)
        
        # Header section
        self._create_header(main_layout)
        
        # Statistics section
        self._create_statistics_section(main_layout)
        
        # Content area with quick actions and recent activity
        content_layout = QHBoxLayout()
        
        # Left column - Quick actions
        self._create_quick_actions(content_layout)
        
        # Right column - Recent activity
        self._create_recent_activity(content_layout)
        
        main_layout.addLayout(content_layout)
        
        # Add stretch to push content to top
        main_layout.addStretch()
    
    def _create_header(self, layout):
        """Create the dashboard header."""
        header_layout = QHBoxLayout()
        
        # Welcome message
        user = auth_manager.get_current_user()
        if user:
            welcome_label = QLabel(f"Welcome back, {user.full_name}")
            welcome_font = QFont()
            welcome_font.setPointSize(18)
            welcome_font.setBold(True)
            welcome_label.setFont(welcome_font)
            welcome_label.setStyleSheet("color: #2c3e50;")
        else:
            welcome_label = QLabel("Dashboard")
        
        header_layout.addWidget(welcome_label)
        
        # Add stretch
        header_layout.addStretch()
        
        # Current date/time
        current_time = datetime.now().strftime("%A, %B %d, %Y - %I:%M %p")
        time_label = QLabel(current_time)
        time_label.setStyleSheet("color: #7f8c8d; font-size: 14px;")
        header_layout.addWidget(time_label)
        
        layout.addLayout(header_layout)
    
    def _create_statistics_section(self, layout):
        """Create the statistics cards section."""
        stats_group = QGroupBox("System Overview")
        stats_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        stats_layout = QHBoxLayout(stats_group)
        stats_layout.setSpacing(15)
        
        # Create stat cards (placeholder data for now)
        self.total_users_card = StatCard("Total Users", "0", "Active accounts", "#3498db")
        self.total_batches_card = StatCard("Student Batches", "0", "Active batches", "#2ecc71")
        self.active_assessments_card = StatCard("Active Assessments", "0", "In progress", "#e74c3c")
        self.completed_assessments_card = StatCard("Completed", "0", "This month", "#f39c12")
        
        stats_layout.addWidget(self.total_users_card)
        stats_layout.addWidget(self.total_batches_card)
        stats_layout.addWidget(self.active_assessments_card)
        stats_layout.addWidget(self.completed_assessments_card)
        stats_layout.addStretch()
        
        layout.addWidget(stats_group)
    
    def _create_quick_actions(self, layout):
        """Create the quick actions section."""
        actions_group = QGroupBox("Quick Actions")
        actions_group.setFixedWidth(350)
        actions_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        actions_layout = QGridLayout(actions_group)
        actions_layout.setSpacing(10)
        
        # Create action buttons based on user permissions
        user = auth_manager.get_current_user()
        if user:
            row, col = 0, 0
            
            # User management (admin only)
            if auth_manager.has_permission(Permission.CREATE_USER):
                user_mgmt_btn = QuickActionButton("Manage\nUsers")
                user_mgmt_btn.clicked.connect(self._open_user_management)
                actions_layout.addWidget(user_mgmt_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0
            
            # Batch management
            if auth_manager.has_permission(Permission.CREATE_BATCH):
                batch_mgmt_btn = QuickActionButton("Manage\nBatches")
                batch_mgmt_btn.clicked.connect(self._open_batch_management)
                actions_layout.addWidget(batch_mgmt_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0
            
            # Assessment creation
            if auth_manager.has_permission(Permission.CREATE_ASSESSMENT):
                assessment_btn = QuickActionButton("Create\nAssessment")
                assessment_btn.clicked.connect(self._open_assessment_creation)
                actions_layout.addWidget(assessment_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0
            
            # View reports
            if auth_manager.has_permission(Permission.VIEW_REPORTS):
                reports_btn = QuickActionButton("View\nReports")
                reports_btn.clicked.connect(self._open_reports)
                actions_layout.addWidget(reports_btn, row, col)
                col += 1
                if col >= 2:
                    row += 1
                    col = 0
            
            # System settings (admin only)
            if auth_manager.has_permission(Permission.SYSTEM_SETTINGS):
                settings_btn = QuickActionButton("System\nSettings")
                settings_btn.clicked.connect(self._open_settings)
                actions_layout.addWidget(settings_btn, row, col)
        
        layout.addWidget(actions_group)
    
    def _create_recent_activity(self, layout):
        """Create the recent activity section."""
        activity_group = QGroupBox("Recent Activity")
        activity_group.setStyleSheet("""
            QGroupBox {
                font-weight: bold;
                font-size: 14px;
                color: #2c3e50;
                border: 2px solid #ecf0f1;
                border-radius: 8px;
                margin-top: 10px;
                padding-top: 10px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 10px;
                padding: 0 5px 0 5px;
            }
        """)
        
        activity_layout = QVBoxLayout(activity_group)
        
        # Create activity table
        self.activity_table = QTableWidget()
        self.activity_table.setColumnCount(3)
        self.activity_table.setHorizontalHeaderLabels(["Time", "Action", "User"])
        self.activity_table.horizontalHeader().setStretchLastSection(True)
        self.activity_table.setAlternatingRowColors(True)
        self.activity_table.setSelectionBehavior(QTableWidget.SelectRows)
        self.activity_table.setMaximumHeight(200)
        
        # Style the table
        self.activity_table.setStyleSheet("""
            QTableWidget {
                gridline-color: #ecf0f1;
                background-color: white;
                alternate-background-color: #f8f9fa;
            }
            QHeaderView::section {
                background-color: #ecf0f1;
                padding: 8px;
                border: none;
                font-weight: bold;
            }
        """)
        
        activity_layout.addWidget(self.activity_table)
        
        layout.addWidget(activity_group)
    
    def refresh_data(self):
        """Refresh dashboard data."""
        try:
            self._update_statistics()
            self._update_recent_activity()
        except Exception as e:
            self.logger.error(f"Error refreshing dashboard data: {e}")
    
    def _update_statistics(self):
        """Update statistics cards with current data."""
        # TODO: Implement actual data retrieval from database
        # For now, using placeholder data
        
        self.total_users_card.findChild(QLabel).setText("5")  # Placeholder
        self.total_batches_card.findChild(QLabel).setText("3")  # Placeholder
        self.active_assessments_card.findChild(QLabel).setText("2")  # Placeholder
        self.completed_assessments_card.findChild(QLabel).setText("8")  # Placeholder
    
    def _update_recent_activity(self):
        """Update recent activity table."""
        # TODO: Implement actual activity log retrieval
        # For now, using placeholder data
        
        activities = [
            ("10:30 AM", "User login", "admin"),
            ("09:45 AM", "Assessment created", "teacher1"),
            ("09:15 AM", "Batch updated", "admin"),
            ("08:30 AM", "Report generated", "teacher2"),
        ]
        
        self.activity_table.setRowCount(len(activities))
        for row, (time, action, user) in enumerate(activities):
            self.activity_table.setItem(row, 0, QTableWidgetItem(time))
            self.activity_table.setItem(row, 1, QTableWidgetItem(action))
            self.activity_table.setItem(row, 2, QTableWidgetItem(user))
    
    # Quick action handlers (placeholder implementations)
    def _open_user_management(self):
        """Open user management module."""
        # TODO: Implement user management interface
        pass
    
    def _open_batch_management(self):
        """Open batch management module."""
        # TODO: Implement batch management interface
        pass
    
    def _open_assessment_creation(self):
        """Open assessment creation module."""
        # TODO: Implement assessment creation interface
        pass
    
    def _open_reports(self):
        """Open reports module."""
        # TODO: Implement reports interface
        pass
    
    def _open_settings(self):
        """Open system settings."""
        # TODO: Implement settings interface
        pass
