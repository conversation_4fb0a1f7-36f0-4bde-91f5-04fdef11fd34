"""
Assessment Models

This module defines models for assessments, questions, and responses.
"""

import json
from datetime import datetime
from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship

from config.database import Base


class Assessment(Base):
    """Assessment model for peer review assessments."""
    
    __tablename__ = "assessments"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Assessment information
    title = Column(String(200), nullable=False)
    description = Column(Text, nullable=True)
    instructions = Column(Text, nullable=True)
    
    # Assessment configuration
    scale_min = Column(Integer, default=1, nullable=False)
    scale_max = Column(Integer, default=7, nullable=False)
    
    # Target batch
    target_batch_id = Column(Integer, ForeignKey("batches.id"), nullable=True)
    
    # Status and metadata
    is_active = Column(Boolean, default=True, nullable=False)
    is_template = Column(Boolean, default=False, nullable=False)
    created_by = Column(Integer, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    modified_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Assessment execution
    start_date = Column(DateTime, nullable=True)
    end_date = Column(DateTime, nullable=True)
    
    # Additional settings
    allow_comments = Column(Boolean, default=True, nullable=False)
    anonymous_responses = Column(Boolean, default=True, nullable=False)
    
    # Relationships
    created_by_user = relationship("User", back_populates="created_assessments")
    target_batch = relationship("Batch", back_populates="assessments")
    questions = relationship("Question", back_populates="assessment", cascade="all, delete-orphan")
    responses = relationship("AssessmentResponse", back_populates="assessment", cascade="all, delete-orphan")
    
    def __repr__(self):
        return f"<Assessment(id={self.id}, title='{self.title}')>"
    
    def to_dict(self):
        """Convert assessment to dictionary."""
        return {
            "id": self.id,
            "title": self.title,
            "description": self.description,
            "instructions": self.instructions,
            "scale_min": self.scale_min,
            "scale_max": self.scale_max,
            "target_batch_id": self.target_batch_id,
            "target_batch_name": self.target_batch.name if self.target_batch else None,
            "is_active": self.is_active,
            "is_template": self.is_template,
            "created_by": self.created_by,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
            "start_date": self.start_date.isoformat() if self.start_date else None,
            "end_date": self.end_date.isoformat() if self.end_date else None,
            "allow_comments": self.allow_comments,
            "anonymous_responses": self.anonymous_responses,
            "question_count": len(self.questions) if self.questions else 0,
            "response_count": len(self.responses) if self.responses else 0,
        }


class Question(Base):
    """Question model for assessment questions."""
    
    __tablename__ = "questions"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Question content
    text = Column(Text, nullable=False)
    question_type = Column(String(50), default="multiple_choice", nullable=False)  # multiple_choice, scale, text
    
    # Question configuration
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=False, index=True)
    order_index = Column(Integer, nullable=False)
    
    # Multiple choice options (stored as JSON)
    options = Column(JSON, nullable=True)  # [{"text": "Option 1", "value": 1}, ...]
    
    # Validation rules
    is_required = Column(Boolean, default=True, nullable=False)
    min_value = Column(Float, nullable=True)
    max_value = Column(Float, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    modified_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    assessment = relationship("Assessment", back_populates="questions")
    
    def __repr__(self):
        return f"<Question(id={self.id}, text='{self.text[:50]}...')>"
    
    def to_dict(self):
        """Convert question to dictionary."""
        return {
            "id": self.id,
            "text": self.text,
            "question_type": self.question_type,
            "assessment_id": self.assessment_id,
            "order_index": self.order_index,
            "options": self.options,
            "is_required": self.is_required,
            "min_value": self.min_value,
            "max_value": self.max_value,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "modified_at": self.modified_at.isoformat() if self.modified_at else None,
        }


class AssessmentResponse(Base):
    """Assessment response model for storing student responses."""
    
    __tablename__ = "assessment_responses"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Response relationships
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=False, index=True)
    student_id = Column(Integer, ForeignKey("students.id"), nullable=False, index=True)
    
    # Response data (stored as JSON)
    responses = Column(JSON, nullable=False)  # {"question_id": {"value": 5, "comment": "..."}, ...}
    
    # Response metadata
    submitted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_complete = Column(Boolean, default=False, nullable=False)
    
    # Additional information
    time_taken_minutes = Column(Integer, nullable=True)
    ip_address = Column(String(45), nullable=True)  # For audit purposes
    
    # Relationships
    assessment = relationship("Assessment", back_populates="responses")
    student = relationship("Student", back_populates="assessment_responses")
    
    def __repr__(self):
        return f"<AssessmentResponse(id={self.id}, assessment_id={self.assessment_id}, student_id={self.student_id})>"
    
    def to_dict(self):
        """Convert response to dictionary."""
        return {
            "id": self.id,
            "assessment_id": self.assessment_id,
            "student_id": self.student_id,
            "student_name": self.student.full_name if self.student else None,
            "responses": self.responses,
            "submitted_at": self.submitted_at.isoformat() if self.submitted_at else None,
            "is_complete": self.is_complete,
            "time_taken_minutes": self.time_taken_minutes,
        }


class PeerEvaluation(Base):
    """Peer evaluation model for storing peer-to-peer assessments."""
    
    __tablename__ = "peer_evaluations"
    
    # Primary key
    id = Column(Integer, primary_key=True, index=True)
    
    # Evaluation relationships
    assessment_id = Column(Integer, ForeignKey("assessments.id"), nullable=False, index=True)
    evaluator_id = Column(Integer, ForeignKey("students.id"), nullable=False, index=True)  # Who is evaluating
    evaluated_id = Column(Integer, ForeignKey("students.id"), nullable=False, index=True)  # Who is being evaluated
    
    # Evaluation factors (7-factor model)
    leadership_score = Column(Integer, nullable=True)
    communication_score = Column(Integer, nullable=True)
    teamwork_score = Column(Integer, nullable=True)
    problem_solving_score = Column(Integer, nullable=True)
    adaptability_score = Column(Integer, nullable=True)
    initiative_score = Column(Integer, nullable=True)
    professionalism_score = Column(Integer, nullable=True)
    
    # Comments for each factor
    leadership_comment = Column(Text, nullable=True)
    communication_comment = Column(Text, nullable=True)
    teamwork_comment = Column(Text, nullable=True)
    problem_solving_comment = Column(Text, nullable=True)
    adaptability_comment = Column(Text, nullable=True)
    initiative_comment = Column(Text, nullable=True)
    professionalism_comment = Column(Text, nullable=True)
    
    # Overall evaluation
    overall_score = Column(Float, nullable=True)
    overall_comment = Column(Text, nullable=True)
    
    # Metadata
    submitted_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    is_complete = Column(Boolean, default=False, nullable=False)
    
    # Relationships
    assessment = relationship("Assessment")
    evaluator = relationship("Student", foreign_keys=[evaluator_id], back_populates="peer_evaluations_given")
    evaluated = relationship("Student", foreign_keys=[evaluated_id], back_populates="peer_evaluations_received")
    
    def __repr__(self):
        return f"<PeerEvaluation(id={self.id}, evaluator_id={self.evaluator_id}, evaluated_id={self.evaluated_id})>"
    
    def to_dict(self):
        """Convert peer evaluation to dictionary."""
        return {
            "id": self.id,
            "assessment_id": self.assessment_id,
            "evaluator_id": self.evaluator_id,
            "evaluated_id": self.evaluated_id,
            "evaluator_name": self.evaluator.full_name if self.evaluator else None,
            "evaluated_name": self.evaluated.full_name if self.evaluated else None,
            "leadership_score": self.leadership_score,
            "communication_score": self.communication_score,
            "teamwork_score": self.teamwork_score,
            "problem_solving_score": self.problem_solving_score,
            "adaptability_score": self.adaptability_score,
            "initiative_score": self.initiative_score,
            "professionalism_score": self.professionalism_score,
            "overall_score": self.overall_score,
            "submitted_at": self.submitted_at.isoformat() if self.submitted_at else None,
            "is_complete": self.is_complete,
        }
    
    def calculate_overall_score(self):
        """Calculate overall score from individual factor scores."""
        scores = [
            self.leadership_score,
            self.communication_score,
            self.teamwork_score,
            self.problem_solving_score,
            self.adaptability_score,
            self.initiative_score,
            self.professionalism_score,
        ]
        
        valid_scores = [score for score in scores if score is not None]
        if valid_scores:
            self.overall_score = sum(valid_scores) / len(valid_scores)
        else:
            self.overall_score = None
