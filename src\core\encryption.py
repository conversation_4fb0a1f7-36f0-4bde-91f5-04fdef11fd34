"""
Data Encryption and Security Utilities

This module provides encryption and decryption functionality for sensitive data
storage and transmission within the peer review system.
"""

import os
import base64
import logging
from typing import Union, Optional
from cryptography.fernet import Fernet
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
from cryptography.hazmat.backends import default_backend

from core.exceptions import EncryptionError


class DataEncryption:
    """Handles data encryption and decryption operations."""
    
    def __init__(self, password: Optional[str] = None):
        self.logger = logging.getLogger(__name__)
        self._fernet = None
        
        if password:
            self._initialize_with_password(password)
        else:
            self._initialize_with_key()
    
    def _initialize_with_password(self, password: str) -> None:
        """Initialize encryption with a password-derived key."""
        try:
            # Generate a salt (should be stored securely in production)
            salt = b'peer_review_salt_2024'  # In production, use random salt
            
            # Derive key from password
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=salt,
                iterations=100000,
                backend=default_backend()
            )
            
            key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
            self._fernet = Fernet(key)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption with password: {e}")
            raise EncryptionError(f"Encryption initialization failed: {e}")
    
    def _initialize_with_key(self) -> None:
        """Initialize encryption with a generated key."""
        try:
            # Generate a new key or load existing one
            key_file = "data/encryption.key"
            
            if os.path.exists(key_file):
                with open(key_file, 'rb') as f:
                    key = f.read()
            else:
                key = Fernet.generate_key()
                os.makedirs(os.path.dirname(key_file), exist_ok=True)
                with open(key_file, 'wb') as f:
                    f.write(key)
                
                # Set restrictive permissions on key file
                os.chmod(key_file, 0o600)
            
            self._fernet = Fernet(key)
            
        except Exception as e:
            self.logger.error(f"Failed to initialize encryption with key: {e}")
            raise EncryptionError(f"Encryption initialization failed: {e}")
    
    def encrypt(self, data: Union[str, bytes]) -> str:
        """Encrypt data and return base64-encoded string."""
        try:
            if isinstance(data, str):
                data = data.encode('utf-8')
            
            encrypted_data = self._fernet.encrypt(data)
            return base64.urlsafe_b64encode(encrypted_data).decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Encryption failed: {e}")
            raise EncryptionError(f"Failed to encrypt data: {e}")
    
    def decrypt(self, encrypted_data: str) -> str:
        """Decrypt base64-encoded encrypted data."""
        try:
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_data.encode('utf-8'))
            decrypted_data = self._fernet.decrypt(encrypted_bytes)
            return decrypted_data.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Decryption failed: {e}")
            raise EncryptionError(f"Failed to decrypt data: {e}")
    
    def encrypt_file(self, file_path: str, output_path: Optional[str] = None) -> str:
        """Encrypt a file and save to output path."""
        try:
            with open(file_path, 'rb') as f:
                file_data = f.read()
            
            encrypted_data = self._fernet.encrypt(file_data)
            
            if output_path is None:
                output_path = f"{file_path}.encrypted"
            
            with open(output_path, 'wb') as f:
                f.write(encrypted_data)
            
            self.logger.info(f"File encrypted: {file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"File encryption failed: {e}")
            raise EncryptionError(f"Failed to encrypt file: {e}")
    
    def decrypt_file(self, encrypted_file_path: str, output_path: Optional[str] = None) -> str:
        """Decrypt a file and save to output path."""
        try:
            with open(encrypted_file_path, 'rb') as f:
                encrypted_data = f.read()
            
            decrypted_data = self._fernet.decrypt(encrypted_data)
            
            if output_path is None:
                output_path = encrypted_file_path.replace('.encrypted', '')
            
            with open(output_path, 'wb') as f:
                f.write(decrypted_data)
            
            self.logger.info(f"File decrypted: {encrypted_file_path} -> {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"File decryption failed: {e}")
            raise EncryptionError(f"Failed to decrypt file: {e}")


class PasswordHasher:
    """Handles password hashing and verification."""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        try:
            import bcrypt
            
            # Generate salt and hash password
            salt = bcrypt.gensalt()
            hashed = bcrypt.hashpw(password.encode('utf-8'), salt)
            
            return hashed.decode('utf-8')
            
        except Exception as e:
            self.logger.error(f"Password hashing failed: {e}")
            raise EncryptionError(f"Failed to hash password: {e}")
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        try:
            import bcrypt
            
            return bcrypt.checkpw(
                password.encode('utf-8'),
                hashed_password.encode('utf-8')
            )
            
        except Exception as e:
            self.logger.error(f"Password verification failed: {e}")
            return False
    
    def is_strong_password(self, password: str) -> tuple[bool, list[str]]:
        """Check if password meets strength requirements."""
        issues = []
        
        if len(password) < 8:
            issues.append("Password must be at least 8 characters long")
        
        if not any(c.isupper() for c in password):
            issues.append("Password must contain at least one uppercase letter")
        
        if not any(c.islower() for c in password):
            issues.append("Password must contain at least one lowercase letter")
        
        if not any(c.isdigit() for c in password):
            issues.append("Password must contain at least one digit")
        
        if not any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password):
            issues.append("Password must contain at least one special character")
        
        return len(issues) == 0, issues


# Global instances
data_encryption = DataEncryption()
password_hasher = PasswordHasher()
