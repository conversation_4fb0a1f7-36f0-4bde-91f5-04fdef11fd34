"""
Database Configuration and Management

This module handles database initialization, migrations, and connection management
for the SQLite database used by the peer review system.
"""

import logging
import sqlite3
from pathlib import Path
from typing import Optional
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker, declarative_base
from sqlalchemy.exc import SQLAlchemyError

from config.settings import app_settings
from core.exceptions import DatabaseError


# SQLAlchemy base class for models
Base = declarative_base()


class DatabaseManager:
    """Manages database connections and operations."""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.engine = None
        self.SessionLocal = None
        self._initialized = False

    def initialize(self) -> None:
        """Initialize the database connection and create tables."""
        try:
            db_path = app_settings.get_database_path()

            # Create database directory if it doesn't exist
            db_path.parent.mkdir(parents=True, exist_ok=True)

            # Create SQLAlchemy engine
            database_url = f"sqlite:///{db_path}"
            self.engine = create_engine(
                database_url,
                echo=False,  # Set to True for SQL debugging
                pool_pre_ping=True,
                connect_args={"check_same_thread": False}
            )

            # Create session factory
            self.SessionLocal = sessionmaker(
                autocommit=False,
                autoflush=False,
                bind=self.engine
            )

            # Test connection
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))

            self.logger.info(f"Database connection established: {db_path}")

            # Run migrations
            self._run_migrations()

            self._initialized = True

            # Run initial setup after database is fully initialized
            self._run_initial_setup()

        except Exception as e:
            self.logger.error(f"Database initialization failed: {e}")
            raise DatabaseError(f"Failed to initialize database: {e}")

    def _run_migrations(self) -> None:
        """Run database migrations to create/update schema."""
        try:
            # Import all models to ensure they're registered with Base
            from models.user import User
            from models.batch import Batch, Student
            from models.assessment import Assessment, Question, AssessmentResponse, PeerEvaluation

            # Create all tables
            Base.metadata.create_all(bind=self.engine)

            # Run custom migrations
            self._run_custom_migrations()

            self.logger.info("Database migrations completed successfully")

        except Exception as e:
            self.logger.error(f"Migration failed: {e}")
            raise DatabaseError(f"Database migration failed: {e}")

    def _run_custom_migrations(self) -> None:
        """Run custom SQL migrations that can't be handled by SQLAlchemy."""
        migrations = [
            # Enable foreign key constraints
            "PRAGMA foreign_keys = ON;",

            # Create indexes for better performance
            """CREATE INDEX IF NOT EXISTS idx_users_username
               ON users(username);""",

            """CREATE INDEX IF NOT EXISTS idx_students_batch_id
               ON students(batch_id);""",

            """CREATE INDEX IF NOT EXISTS idx_assessment_responses_assessment_id
               ON assessment_responses(assessment_id);""",

            """CREATE INDEX IF NOT EXISTS idx_assessment_responses_student_id
               ON assessment_responses(student_id);""",

            # Create triggers for audit logging
            """CREATE TRIGGER IF NOT EXISTS update_user_modified_at
               AFTER UPDATE ON users
               BEGIN
                   UPDATE users SET modified_at = CURRENT_TIMESTAMP
                   WHERE id = NEW.id;
               END;""",
        ]

        with self.engine.connect() as conn:
            for migration in migrations:
                try:
                    conn.execute(text(migration))
                    conn.commit()
                except Exception as e:
                    self.logger.warning(f"Migration warning: {e}")

    def _run_initial_setup(self) -> None:
        """Run initial database setup including default data."""
        try:
            # Only run initial setup if database is properly initialized
            if self._initialized:
                from database.migrations import db_migrations
                db_migrations.run_initial_setup()
        except Exception as e:
            self.logger.warning(f"Initial setup warning: {e}")
            # Don't raise exception for initial setup failures

    def get_session(self):
        """Get a new database session."""
        if not self._initialized:
            raise DatabaseError("Database not initialized")

        return self.SessionLocal()

    def backup_database(self, backup_path: Optional[Path] = None) -> Path:
        """Create a backup of the database."""
        if not self._initialized:
            raise DatabaseError("Database not initialized")

        try:
            db_path = app_settings.get_database_path()

            if backup_path is None:
                backup_dir = Path("backups")
                backup_dir.mkdir(exist_ok=True)

                from datetime import datetime
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_path = backup_dir / f"peer_review_backup_{timestamp}.db"

            # Use SQLite backup API for consistent backup
            source_conn = sqlite3.connect(str(db_path))
            backup_conn = sqlite3.connect(str(backup_path))

            source_conn.backup(backup_conn)

            source_conn.close()
            backup_conn.close()

            self.logger.info(f"Database backup created: {backup_path}")
            return backup_path

        except Exception as e:
            self.logger.error(f"Database backup failed: {e}")
            raise DatabaseError(f"Failed to backup database: {e}")

    def restore_database(self, backup_path: Path) -> None:
        """Restore database from backup."""
        try:
            if not backup_path.exists():
                raise DatabaseError(f"Backup file not found: {backup_path}")

            db_path = app_settings.get_database_path()

            # Close existing connections
            if self.engine:
                self.engine.dispose()

            # Replace current database with backup
            import shutil
            shutil.copy2(backup_path, db_path)

            # Reinitialize
            self.initialize()

            self.logger.info(f"Database restored from: {backup_path}")

        except Exception as e:
            self.logger.error(f"Database restore failed: {e}")
            raise DatabaseError(f"Failed to restore database: {e}")

    def get_database_info(self) -> dict:
        """Get database information and statistics."""
        if not self._initialized:
            raise DatabaseError("Database not initialized")

        try:
            with self.engine.connect() as conn:
                # Get database size
                db_path = app_settings.get_database_path()
                db_size = db_path.stat().st_size if db_path.exists() else 0

                # Get table counts
                tables_info = {}
                table_names = ['users', 'batches', 'students', 'assessments',
                              'questions', 'assessment_responses']

                for table in table_names:
                    try:
                        result = conn.execute(text(f"SELECT COUNT(*) FROM {table}"))
                        count = result.scalar()
                        tables_info[table] = count
                    except:
                        tables_info[table] = 0

                return {
                    "database_path": str(db_path),
                    "database_size_bytes": db_size,
                    "database_size_mb": round(db_size / (1024 * 1024), 2),
                    "tables": tables_info,
                    "total_records": sum(tables_info.values())
                }

        except Exception as e:
            self.logger.error(f"Failed to get database info: {e}")
            raise DatabaseError(f"Failed to get database information: {e}")

    def close(self) -> None:
        """Close database connections."""
        if self.engine:
            self.engine.dispose()
            self.logger.info("Database connections closed")


# Global database manager instance
db_manager = DatabaseManager()
