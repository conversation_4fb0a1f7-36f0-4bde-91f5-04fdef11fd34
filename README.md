# Military Peer Review Assessment System

A comprehensive native desktop application for military personnel evaluation using peer assessment methodology.

## Features

- **Offline-capable**: Fully functional without internet connection
- **Secure**: Local data encryption and role-based access control
- **Modular**: 8 core modules for complete assessment lifecycle
- **Cross-platform**: Windows, macOS, and Linux support

## Modules

1. **Admin Panel**: User management and system configuration
2. **Batch Management**: Student batch creation and management
3. **Assessment Creation**: Custom assessment design and templates
4. **Peer Assessment Execution**: Secure assessment deployment and collection
5. **Data Processing & Analytics**: Automated analysis and outlier detection
6. **Visualization & Reporting**: Interactive charts and dashboards
7. **Report Generation**: Comprehensive PDF reports
8. **System Features**: Dashboard, settings, and help system

## Installation

### Prerequisites
- Python 3.9 or higher
- pip package manager

### Setup
1. Clone the repository:
   ```bash
   git clone <repository-url>
   cd peer-review-system-2
   ```

2. Create virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```

3. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```

4. Run the application:
   ```bash
   python src/main.py
   ```

## Development

### Project Structure
```
src/
├── main.py              # Application entry point
├── config/              # Configuration management
├── core/                # Core authentication and security
├── models/              # Data models
├── database/            # Database layer
├── ui/                  # User interface components
├── services/            # Business logic
└── utils/               # Utility functions
```

### Testing
```bash
pytest tests/
```

### Code Quality
```bash
black src/
flake8 src/
mypy src/
```

## License

MIT License - See LICENSE file for details.

## Authors

- **Author**: Maj. Sachin Kumar Singh
- **Developer**: Hrishikesh Mohite
- **Company**: Ajinkyacreatiion PVT. LTD.

## Support

For support and inquiries:
- Website: https://www.ajinkyacreatiion.com
- Developer: https://www.hrishikeshmohite.com
- Email: <EMAIL>
